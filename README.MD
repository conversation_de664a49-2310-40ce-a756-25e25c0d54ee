Dual Currency Display (BGN & EUR) for OpenCart 4.x

This extension displays product prices in both Bulgarian Levs (BGN) and Euros (EUR) across your storefront.
Prerequisites

    Currencies Configured: Ensure both Bulgarian Lev (BGN) and Euro (EUR) are set up in System -> Localisation -> Currencies.

    Default Currency: Your store's default currency must be set to BGN.

    Accurate Rates: You MUST have up-to-date currency conversion rates set in your admin panel. The extension uses these rates for all calculations.

Installation

    Unzip the extension package.

    You will find an upload folder and an install.xml file.

    Create a new zip file named dual_currency_bgn_eur.ocmod.zip.

    Inside this new zip file, place the upload folder and the install.xml file.

    In your OpenCart Admin, navigate to Extensions -> Installer.

    Click the Upload button and select the dual_currency_bgn_eur.ocmod.zip file you just created.

    After the upload is complete, navigate to Extensions -> Modifications.

    Click the blue Refresh button (top right) to apply the changes.

That's it! Your store should now display prices in EUR below the main BGN price.
Uninstallation

    Go to Extensions -> Installer and delete the extension from the list.

    Go to Extensions -> Modifications, find the "Dual Currency Display" modification, and delete it.

    Click the Refresh button again to clear the cache.