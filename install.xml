<?xml version="1.0" encoding="utf-8"?>
<modification>
    <name>Dual Currency Display (BGN &amp; EUR)</name>
    <code>dual_currency_bgn_eur</code>
    <version>1.1</version>
    <author>Gemini</author>
    <link></link>

    <!--
    ================================================================
    PRODUCT PAGE
    ================================================================
    -->
    <file path="catalog/controller/product/product.php">
        <operation>
            <search><![CDATA[$data['price'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);]]></search>
            <add position="after"><![CDATA[
            // Dual Currency: Add EUR price
            $data['price_eur'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[$data['special'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);]]></search>
            <add position="after"><![CDATA[
            // Dual Currency: Add EUR special price
            $data['special_eur'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/template/product/product.twig">
        <operation>
            <search><![CDATA[<li class="list-unstyled">{{ price }}</li>]]></search>
            <add position="after"><![CDATA[
            {% if price_eur and price != price_eur %}
            <li class="list-unstyled text-muted" style="font-size: 0.9em;">({{ price_eur }})</li>
            {% endif %}
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA[<li><span class="price-new">{{ special }}</span> <span class="price-old">{{ price }}</span></li>]]></search>
            <add position="after"><![CDATA[
            {% if special_eur and special != special_eur %}
            <li class="list-unstyled text-muted" style="font-size: 0.9em;">
                <span class="price-new">({{ special_eur }})</span>
                {% if price_eur and price != price_eur %}
                <span class="price-old">({{ price_eur }})</span>
                {% endif %}
            </li>
            {% endif %}
            ]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    CATEGORY, SEARCH, MANUFACTURER, SPECIALS PAGES
    ================================================================
    -->
    <file path="catalog/controller/product/{category,search,manufacturer,special}.php">
        <operation>
            <search><![CDATA[$data['products'][] = [ //]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['special'     => $special,]]></search>
            <add position="after"><![CDATA[
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/template/product/list.twig">
        <operation>
            <search><![CDATA[<div class="price">]]></search>
            <add position="after"><![CDATA[
              {% if not product.special %}
                {% if product.price_eur and product.price != product.price_eur %}
                  <div class="price-eur text-muted" style="font-size: 0.9em; margin-top: 2px;">({{ product.price_eur }})</div>
                {% endif %}
              {% else %}
                <div class="price-eur text-muted" style="font-size: 0.9em; margin-top: 2px;">
                    <span class="price-new">({{ product.special_eur }})</span>
                    {% if product.price_eur and product.price != product.price_eur %}
                        <span class="price-old">({{ product.price_eur }})</span>
                    {% endif %}
                </div>
              {% endif %}
            ]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    MODULES (Featured, Latest, Bestseller, etc.)
    ================================================================
    -->
    <file path="catalog/controller/extension/opencart/module/{bestseller,featured,latest,special}.php">
        <operation>
            <search><![CDATA[$data['products'][] = [ //]]></search>
             <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['special'     => $special,]]></search>
            <add position="after"><![CDATA[
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>
    
    <!-- Note: Modules use the product/thumb.twig template which uses the same logic as product/list.twig -->

    <!--
    ================================================================
    SHOPPING CART
    ================================================================
    -->
    <file path="catalog/controller/checkout/cart.php">
        <operation>
            <search><![CDATA[$data['products'][] = []]></search>
            <add position="before"><![CDATA[
            // Dual Currency
            $price_eur = $this->currency->format($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            $total_eur = $this->currency->format(($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax'))) * $product['quantity'], 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['price'    => $price,]]></search>
            <add position="after"><![CDATA[
            'price_eur' => $price_eur,
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['total'    => $total,]]></search>
            <add position="after"><![CDATA[
            'total_eur' => $total_eur,
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[$data['totals'][] = [
                'title' => $total['title'],
                'text'  => $this->currency->format($total['value'], $this->session->data['currency'])
            ];]]></search>
            <add position="replace"><![CDATA[
            $text_bgn = $this->currency->format($total['value'], $this->session->data['currency']);
            $text_eur = $this->currency->format($total['value'], 'EUR');

            $final_text = $text_bgn;
            if ($text_bgn != $text_eur) {
                 $final_text .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
            }

            $data['totals'][] = [
                'title' => $total['title'],
                'text'  => $final_text
            ];
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/template/checkout/cart.twig">
        <operation>
            <search><![CDATA[<td class="text-end">{{ product.price }}</td>]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur and product.price != product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td class="text-end">{{ product.total }}</td>]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur and product.total != product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td class="text-end">{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td class="text-end">{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    CUSTOMER ORDER INFO
    ================================================================
    -->
    <file path="catalog/controller/account/order.php">
        <operation>
            <search><![CDATA[$product_data[] = []]></search>
            <add position="before"><![CDATA[
            // Dual Currency
            $price_eur_val = $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), 'EUR', '', false);
            $price_eur = $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), 'EUR');
            $total_eur = $this->currency->format($product['total'] + ($this->config->get('config_tax') ? ($product['tax'] * $product['quantity']) : 0), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['price' => $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), $order_info['currency_code'], $order_info['currency_value']),]]></search>
            <add position="after"><![CDATA[
            'price_eur' => ($order_info['currency_code'] != 'EUR') ? $price_eur : '',
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA['total' => $this->currency->format($product['total'] + ($this->config->get('config_tax') ? ($product['tax'] * $product['quantity']) : 0), $order_info['currency_code'], $order_info['currency_value']),]]></search>
            <add position="after"><![CDATA[
            'total_eur' => ($order_info['currency_code'] != 'EUR') ? $total_eur : '',
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[$data['totals'][] = [
                'title' => $total['title'],
                'text'  => $this->currency->format($total['value'], $order_info['currency_code'], $order_info['currency_value'])
            ];]]></search>
            <add position="replace"><![CDATA[
            $text_order_currency = $this->currency->format($total['value'], $order_info['currency_code'], $order_info['currency_value']);
            $text_eur = $this->currency->format($total['value'], 'EUR');

            $final_text = $text_order_currency;
            if ($text_order_currency != $text_eur) {
                 $final_text .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
            }

            $data['totals'][] = [
                'title' => $total['title'],
                'text'  => $final_text
            ];
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/template/account/order_info.twig">
        <operation>
            <search><![CDATA[<td class="text-end">{{ product.price }}</td>]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td class="text-end">{{ product.total }}</td>]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td class="text-end">{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td class="text-end">{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    ADMIN ORDER INFO
    ================================================================
    -->
    <file path="admin/controller/sale/order.php">
        <operation>
            <search><![CDATA[$data['products'][] = []]></search>
            <add position="before"><![CDATA[
            // Dual Currency
            $price_eur = $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), 'EUR');
            $total_eur = $this->currency->format($product['total'] + ($this->config->get('config_tax') ? ($product['tax'] * $product['quantity']) : 0), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['price' => $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), $order_info['currency_code'], $order_info['currency_value']),]]></search>
            <add position="after"><![CDATA[
            'price_eur' => ($order_info['currency_code'] != 'EUR') ? $price_eur : '',
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA['total' => $this->currency->format($product['total'] + ($this->config->get('config_tax') ? ($product['tax'] * $product['quantity']) : 0), $order_info['currency_code'], $order_info['currency_value']),]]></search>
            <add position="after"><![CDATA[
            'total_eur' => ($order_info['currency_code'] != 'EUR') ? $total_eur : '',
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[$data['totals'][] = [
                'title' => $total['title'],
                'text'  => $this->currency->format($total['value'], $order_info['currency_code'], $order_info['currency_value'])
            ];]]></search>
            <add position="replace"><![CDATA[
            $text_order_currency = $this->currency->format($total['value'], $order_info['currency_code'], $order_info['currency_value']);
            $text_eur = $this->currency->format($total['value'], 'EUR');

            $final_text = $text_order_currency;
            if ($text_order_currency != $text_eur) {
                 $final_text .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
            }

            $data['totals'][] = [
                'title' => $total['title'],
                'text'  => $final_text
            ];
            ]]></add>
        </operation>
    </file>

    <file path="admin/view/template/sale/order_info.twig">
        <operation>
            <search><![CDATA[<td class="text-end">{{ product.price }}</td>]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td class="text-end">{{ product.total }}</td>]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td class="text-end">{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td class="text-end">{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>

</modification>
